import java.util.*;

class Solution {
    private int trueCount = 0;

    private void flip(boolean[][] matrix, int y0, int x0, int h0, int w0) {
        for (int y = y0; y < y0 + h0; y++) {
            for (int x = x0; x < x0 + w0; x++) {
                matrix[y][x] = !matrix[y][x];
                trueCount += matrix[y][x] ? 1 : -1;
            }
        }
    }

    private boolean allFalse(boolean[][] matrix, int m, int n) {
        return trueCount == 0;
    }

    private int countTrues(boolean[][] matrix, int m, int n) {
        int count = 0;
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    count++;
                }
            }
        }

        return count;
    }

    private int solveSub(int m, int n, int y0, int x0, int h0, int w0, boolean[][] matrix, int nCurRects, int curMin) {
        if (allFalse(matrix, m, n)) {
            return nCurRects;
        }

        if (nCurRects >= curMin - 1) {
            return curMin;
        }

        int h = h0;
        int w = w0;
        int y = y0;
        
        if (w0 == 0 && h0 == 0) {
        }

        for (int h = 1; h <= m; h++) {
            for (int w = 1; w <= n; w++) {
                for (int y = 0; y <= m - h; y++) {
                    for (int x = 0; x <= n - w; x++) {
                        flip(matrix, y, x, h, w);
                        int foundCount = solveSub(m, n, y, x, h, w, matrix, nCurRects + 1, curMin);
                        flip(matrix, y, x, h, w);

                        if (foundCount < curMin) {
                            curMin = foundCount;
                        }
                    }
                }
            }
        }

        return curMin;
    }

    public int solve(int m, int n, boolean[][] matrix) {
        trueCount = countTrues(matrix, m, n);
        return solveSub(m, n, matrix, 0, trueCount);
    }
}